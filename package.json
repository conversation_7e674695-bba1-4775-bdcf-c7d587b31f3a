{"name": "rk-institute-management-system", "version": "1.1.0", "description": "RK Institute Management System - Production Ready with Assignments", "private": true, "scripts": {"build": "prisma migrate deploy && prisma generate && next build", "build:local": "prisma generate && next build", "build:ci": "prisma generate && next build", "build:force": "prisma migrate deploy && prisma generate && next build || exit 0", "analyze": "cross-env ANALYZE=true npm run build:local", "start": "next start", "dev": "next dev", "postinstall": "prisma generate", "db:migrate": "prisma migrate deploy", "db:generate": "prisma generate", "health": "node scripts/health-check.js", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:smoke": "jest --testPathPattern=smoke", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:all": "npm run test:ci && npm run test:e2e", "test:production": "node scripts/production-ready-testing.js", "test:comprehensive": "node scripts/comprehensive-role-testing.js", "db:seed": "tsx prisma/seed.ts", "db:check": "node -e \"require('./lib/prisma').prisma.$queryRaw\\`SELECT 1\\`.then(() => console.log('DB OK')).catch(() => process.exit(1))\"", "deploy:production": "bash scripts/deploy-production.sh", "health:check": "curl -f http://localhost:3000/api/health || exit 1", "security:audit": "npm audit --audit-level=high", "performance:analyze": "npm run analyze", "start:production": "NODE_ENV=production next start -p 3000"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@heroicons/react": "^2.2.0", "@prisma/client": "^5.7.1", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.5", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.0.1", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "html2canvas": "^1.4.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "lucide-react": "^0.515.0", "next": "14.0.4", "node-cron": "^4.1.0", "nodemailer": "^7.0.3", "postcss": "^8", "prisma": "^5.7.1", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.3.0", "typescript": "^5.3.3", "web-vitals": "^5.0.3", "zod": "^3.22.4"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.4", "@playwright/test": "^1.53.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.8", "cross-env": "^7.0.3", "eslint": "^8.55.0", "eslint-config-next": "14.0.4", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-testing-library": "^7.5.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "playwright": "^1.53.0", "prettier": "^3.1.0", "tsx": "^4.6.2", "webpack-bundle-analyzer": "^4.10.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["institute-management", "education", "fee-management", "student-management", "nextjs", "typescript", "postgresql"], "author": "RK Institute", "license": "PROPRIETARY"}