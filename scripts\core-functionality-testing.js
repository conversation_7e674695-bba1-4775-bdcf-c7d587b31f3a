#!/usr/bin/env node

/**
 * 🧪 CORE FUNCTIONALITY TESTING SUITE
 * RK Institute Management System - Comprehensive Validation
 * 
 * Tests core functionality, security enhancements, and production readiness
 * Academic Year 2024-25 | CBSE Curriculum
 */

const fs = require('fs').promises;
const path = require('path');

class CoreFunctionalityTestSuite {
  constructor() {
    this.results = [];
    this.startTime = Date.now();
    
    // Test user accounts for role-based testing
    this.testAccounts = {
      admin: { email: '<EMAIL>', password: 'admin123', role: 'admin' },
      teacher: { email: '<EMAIL>', password: 'admin123', role: 'teacher' },
      parent: { email: '<EMAIL>', password: 'admin123', role: 'parent' },
      student: { email: '<EMAIL>', password: 'admin123', role: 'student' }
    };

    // Security enhancements we implemented
    this.securityFeatures = [
      'JWT Authentication',
      'Role-based Authorization', 
      'CSRF Protection Middleware',
      'Input Validation',
      'SQL Injection Protection',
      'XSS Prevention',
      'Secure Headers',
      'Session Management'
    ];

    // Production readiness features
    this.productionFeatures = [
      'Health API Endpoint',
      'Database Connectivity',
      'Error Handling',
      'Logging System',
      'Performance Monitoring',
      'Build Process',
      'Environment Configuration'
    ];
  }

  addResult(category, test, status, details, metadata = {}) {
    const result = {
      timestamp: new Date().toISOString(),
      category,
      test,
      status,
      details,
      metadata
    };
    
    this.results.push(result);
    
    const statusIcon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : status === 'WARN' ? '⚠️' : '📋';
    console.log(`${statusIcon} [${category}] ${test}: ${details}`);
  }

  // 🔐 AUTHENTICATION & AUTHORIZATION TESTING
  async testAuthenticationSystem() {
    console.log('\n🔐 AUTHENTICATION & AUTHORIZATION TESTING');
    console.log('🎯 Testing role-based access control and security measures');
    console.log('=' .repeat(70));

    // Test authentication components
    await this.testAuthComponents();
    
    // Test role-based access
    await this.testRoleBasedAccess();
    
    // Test security middleware
    await this.testSecurityMiddleware();
  }

  async testAuthComponents() {
    console.log('\n🔑 Testing Authentication Components...');
    
    const authComponents = [
      { file: 'app/api/auth/route.ts', component: 'Authentication API' },
      { file: 'middleware.ts', component: 'Security Middleware' },
      { file: 'lib/csrf-protection.ts', component: 'CSRF Protection' },
      { file: 'components/layout/AdminLayout.tsx', component: 'Admin Layout' },
      { file: 'components/layout/TeacherLayout.tsx', component: 'Teacher Layout' },
      { file: 'components/layout/ParentLayout.tsx', component: 'Parent Layout' },
      { file: 'components/layout/StudentLayout.tsx', component: 'Student Layout' }
    ];

    for (const auth of authComponents) {
      try {
        const filePath = path.join(process.cwd(), auth.file);
        await fs.access(filePath);
        
        // Check file content for key security features
        const content = await fs.readFile(filePath, 'utf8');
        const hasSecurityFeatures = this.checkSecurityFeatures(content, auth.component);
        
        this.addResult(
          'Authentication Components',
          auth.component,
          hasSecurityFeatures.status,
          hasSecurityFeatures.details,
          { file: auth.file, features: hasSecurityFeatures.features }
        );
      } catch (error) {
        this.addResult(
          'Authentication Components',
          auth.component,
          'FAIL',
          `Component file not found: ${auth.file}`,
          { file: auth.file, error: error.message }
        );
      }
    }
  }

  checkSecurityFeatures(content, componentName) {
    const features = [];
    let criticalIssues = 0;

    // Check for JWT handling
    if (content.includes('jwt') || content.includes('JWT') || content.includes('token')) {
      features.push('JWT Token Handling');
    } else if (componentName.includes('API') || componentName.includes('Middleware')) {
      criticalIssues++;
    }

    // Check for role-based access
    if (content.includes('role') || content.includes('Role') || content.includes('admin') || content.includes('teacher')) {
      features.push('Role-based Access Control');
    }

    // Check for CSRF protection
    if (content.includes('csrf') || content.includes('CSRF') || content.includes('token')) {
      features.push('CSRF Protection');
    }

    // Check for input validation
    if (content.includes('validate') || content.includes('sanitize') || content.includes('escape')) {
      features.push('Input Validation');
    }

    // Check for error handling
    if (content.includes('try') && content.includes('catch')) {
      features.push('Error Handling');
    }

    const status = criticalIssues === 0 ? 'PASS' : 'WARN';
    const details = `${features.length} security features detected: ${features.join(', ')}`;

    return { status, details, features };
  }

  async testRoleBasedAccess() {
    console.log('\n👥 Testing Role-based Access Control...');
    
    const roleTests = [
      { role: 'admin', pages: ['admin/dashboard', 'admin/people', 'admin/financials', 'admin/academic'] },
      { role: 'teacher', pages: ['teacher/dashboard', 'teacher/classes', 'teacher/grades', 'teacher/messages'] },
      { role: 'parent', pages: ['parent/dashboard', 'parent/children', 'parent/fees', 'parent/emergency-contacts'] },
      { role: 'student', pages: ['student/dashboard', 'student/grades', 'student/schedule', 'student/assignments'] }
    ];

    for (const roleTest of roleTests) {
      let accessiblePages = 0;
      let totalPages = roleTest.pages.length;

      for (const page of roleTest.pages) {
        try {
          const pagePath = path.join(process.cwd(), 'app', `${page}/page.tsx`);
          await fs.access(pagePath);
          
          // Check if page has proper layout and role restrictions
          const content = await fs.readFile(pagePath, 'utf8');
          const hasRoleLayout = content.includes(`${roleTest.role.charAt(0).toUpperCase() + roleTest.role.slice(1)}Layout`);
          
          if (hasRoleLayout) {
            accessiblePages++;
          }
        } catch (error) {
          // Page doesn't exist - this might be expected for some roles
        }
      }

      const accessRate = (accessiblePages / totalPages) * 100;
      const status = accessRate >= 75 ? 'PASS' : accessRate >= 50 ? 'WARN' : 'FAIL';
      
      this.addResult(
        'Role-based Access',
        `${roleTest.role.toUpperCase()} Role Access`,
        status,
        `${accessiblePages}/${totalPages} pages accessible (${accessRate.toFixed(1)}%)`,
        { role: roleTest.role, accessiblePages, totalPages, accessRate }
      );
    }
  }

  async testSecurityMiddleware() {
    console.log('\n🛡️ Testing Security Middleware...');
    
    try {
      const middlewarePath = path.join(process.cwd(), 'middleware.ts');
      const content = await fs.readFile(middlewarePath, 'utf8');
      
      const securityChecks = [
        { feature: 'JWT Token Validation', pattern: /token.*split|jwt.*verify/i },
        { feature: 'Route Protection', pattern: /protected.*routes|auth.*required/i },
        { feature: 'Role-based Routing', pattern: /role.*check|admin.*teacher|parent.*student/i },
        { feature: 'Error Handling', pattern: /try.*catch|error.*handling/i },
        { feature: 'Redirect Logic', pattern: /redirect|NextResponse\.redirect/i }
      ];

      let passedChecks = 0;
      const detectedFeatures = [];

      for (const check of securityChecks) {
        if (check.pattern.test(content)) {
          passedChecks++;
          detectedFeatures.push(check.feature);
        }
      }

      const securityScore = (passedChecks / securityChecks.length) * 100;
      const status = securityScore >= 80 ? 'PASS' : securityScore >= 60 ? 'WARN' : 'FAIL';

      this.addResult(
        'Security Middleware',
        'Middleware Security Features',
        status,
        `${passedChecks}/${securityChecks.length} security features implemented (${securityScore.toFixed(1)}%)`,
        { features: detectedFeatures, score: securityScore }
      );

    } catch (error) {
      this.addResult(
        'Security Middleware',
        'Middleware Security Features',
        'FAIL',
        `Middleware file not accessible: ${error.message}`,
        { error: error.message }
      );
    }
  }

  // 🏥 PRODUCTION READINESS TESTING
  async testProductionReadiness() {
    console.log('\n🏥 PRODUCTION READINESS TESTING');
    console.log('🎯 Testing health endpoints, database connectivity, and deployment features');
    console.log('=' .repeat(70));

    // Test health API endpoint
    await this.testHealthEndpoint();
    
    // Test database configuration
    await this.testDatabaseConfig();
    
    // Test build configuration
    await this.testBuildConfig();
    
    // Test environment configuration
    await this.testEnvironmentConfig();
  }

  async testHealthEndpoint() {
    console.log('\n🏥 Testing Health API Endpoint...');
    
    try {
      const healthApiPath = path.join(process.cwd(), 'app/api/health/route.ts');
      const content = await fs.readFile(healthApiPath, 'utf8');
      
      const healthChecks = [
        { feature: 'Database Connectivity', pattern: /prisma.*\$connect|database.*connection/i },
        { feature: 'System Health', pattern: /health.*check|system.*status/i },
        { feature: 'Performance Metrics', pattern: /performance|responseTime|metrics/i },
        { feature: 'Error Handling', pattern: /try.*catch|error.*handling/i },
        { feature: 'JSON Response', pattern: /NextResponse\.json|json.*response/i }
      ];

      let implementedFeatures = 0;
      const detectedFeatures = [];

      for (const check of healthChecks) {
        if (check.pattern.test(content)) {
          implementedFeatures++;
          detectedFeatures.push(check.feature);
        }
      }

      const healthScore = (implementedFeatures / healthChecks.length) * 100;
      const status = healthScore >= 80 ? 'PASS' : healthScore >= 60 ? 'WARN' : 'FAIL';

      this.addResult(
        'Production Readiness',
        'Health API Endpoint',
        status,
        `${implementedFeatures}/${healthChecks.length} health features implemented (${healthScore.toFixed(1)}%)`,
        { features: detectedFeatures, score: healthScore }
      );

    } catch (error) {
      this.addResult(
        'Production Readiness',
        'Health API Endpoint',
        'FAIL',
        `Health API endpoint not found: ${error.message}`,
        { error: error.message }
      );
    }
  }

  async testDatabaseConfig() {
    console.log('\n🗄️ Testing Database Configuration...');
    
    const dbTests = [
      { file: 'prisma/schema.prisma', component: 'Prisma Schema' },
      { file: '.env.example', component: 'Environment Template' },
      { file: 'package.json', component: 'Database Dependencies' }
    ];

    for (const dbTest of dbTests) {
      try {
        const filePath = path.join(process.cwd(), dbTest.file);
        const content = await fs.readFile(filePath, 'utf8');
        
        let dbFeatures = [];
        
        if (dbTest.file.includes('schema.prisma')) {
          if (content.includes('postgresql')) dbFeatures.push('PostgreSQL Configuration');
          if (content.includes('model')) dbFeatures.push('Data Models Defined');
          if (content.includes('@@map')) dbFeatures.push('Table Mapping');
        } else if (dbTest.file.includes('.env')) {
          if (content.includes('DATABASE_URL')) dbFeatures.push('Database URL Configuration');
          if (content.includes('NEXTAUTH')) dbFeatures.push('Authentication Configuration');
        } else if (dbTest.file.includes('package.json')) {
          if (content.includes('@prisma/client')) dbFeatures.push('Prisma Client');
          if (content.includes('prisma')) dbFeatures.push('Prisma CLI');
        }

        const status = dbFeatures.length > 0 ? 'PASS' : 'WARN';
        
        this.addResult(
          'Database Configuration',
          dbTest.component,
          status,
          `${dbFeatures.length} database features: ${dbFeatures.join(', ')}`,
          { file: dbTest.file, features: dbFeatures }
        );

      } catch (error) {
        this.addResult(
          'Database Configuration',
          dbTest.component,
          'WARN',
          `Configuration file not found: ${dbTest.file}`,
          { file: dbTest.file, error: error.message }
        );
      }
    }
  }

  async testBuildConfig() {
    console.log('\n🔨 Testing Build Configuration...');
    
    try {
      const packagePath = path.join(process.cwd(), 'package.json');
      const content = await fs.readFile(packagePath, 'utf8');
      const packageJson = JSON.parse(content);
      
      const buildScripts = [
        { script: 'build', description: 'Production Build' },
        { script: 'build:local', description: 'Local Build' },
        { script: 'dev', description: 'Development Server' },
        { script: 'start', description: 'Production Server' }
      ];

      let availableScripts = 0;
      const detectedScripts = [];

      for (const buildScript of buildScripts) {
        if (packageJson.scripts && packageJson.scripts[buildScript.script]) {
          availableScripts++;
          detectedScripts.push(buildScript.description);
        }
      }

      const buildScore = (availableScripts / buildScripts.length) * 100;
      const status = buildScore >= 75 ? 'PASS' : buildScore >= 50 ? 'WARN' : 'FAIL';

      this.addResult(
        'Build Configuration',
        'Build Scripts',
        status,
        `${availableScripts}/${buildScripts.length} build scripts available (${buildScore.toFixed(1)}%)`,
        { scripts: detectedScripts, score: buildScore }
      );

    } catch (error) {
      this.addResult(
        'Build Configuration',
        'Build Scripts',
        'FAIL',
        `Package.json not accessible: ${error.message}`,
        { error: error.message }
      );
    }
  }

  async testEnvironmentConfig() {
    console.log('\n🌍 Testing Environment Configuration...');
    
    const envFiles = [
      { file: '.env.example', description: 'Environment Template' },
      { file: '.env.local', description: 'Local Environment' },
      { file: '.env.production', description: 'Production Environment' }
    ];

    let configuredEnvs = 0;
    const availableEnvs = [];

    for (const envFile of envFiles) {
      try {
        const filePath = path.join(process.cwd(), envFile.file);
        await fs.access(filePath);
        configuredEnvs++;
        availableEnvs.push(envFile.description);
        
        this.addResult(
          'Environment Configuration',
          envFile.description,
          'PASS',
          `Environment file exists: ${envFile.file}`,
          { file: envFile.file }
        );
      } catch (error) {
        this.addResult(
          'Environment Configuration',
          envFile.description,
          'WARN',
          `Environment file not found: ${envFile.file}`,
          { file: envFile.file }
        );
      }
    }

    // Overall environment configuration assessment
    const envScore = (configuredEnvs / envFiles.length) * 100;
    const status = envScore >= 66 ? 'PASS' : envScore >= 33 ? 'WARN' : 'FAIL';

    this.addResult(
      'Environment Configuration',
      'Overall Environment Setup',
      status,
      `${configuredEnvs}/${envFiles.length} environment configurations available (${envScore.toFixed(1)}%)`,
      { environments: availableEnvs, score: envScore }
    );
  }

  // 📊 COMPREHENSIVE FUNCTIONALITY REPORT
  async generateFunctionalityReport() {
    const totalTime = Date.now() - this.startTime;

    console.log('\n📊 CORE FUNCTIONALITY ASSESSMENT REPORT');
    console.log('🏫 RK Institute Management System - Functionality Validation');
    console.log('📚 Academic Year 2024-25 | CBSE Curriculum');
    console.log('=' .repeat(80));

    // Summary Statistics
    const summary = {
      total: this.results.length,
      passed: this.results.filter(r => r.status === 'PASS').length,
      failed: this.results.filter(r => r.status === 'FAIL').length,
      warnings: this.results.filter(r => r.status === 'WARN').length
    };

    console.log(`\n📈 FUNCTIONALITY SUMMARY:`);
    console.log(`   Total Tests: ${summary.total}`);
    console.log(`   ✅ Passed: ${summary.passed} (${((summary.passed / summary.total) * 100).toFixed(1)}%)`);
    console.log(`   ❌ Failed: ${summary.failed} (${((summary.failed / summary.total) * 100).toFixed(1)}%)`);
    console.log(`   ⚠️  Warnings: ${summary.warnings} (${((summary.warnings / summary.total) * 100).toFixed(1)}%)`);
    console.log(`   ⏱️  Execution Time: ${totalTime}ms`);

    // Category Analysis
    console.log(`\n🔍 CATEGORY ANALYSIS:`);
    const categories = [
      'Authentication Components',
      'Role-based Access',
      'Security Middleware',
      'Production Readiness',
      'Database Configuration',
      'Build Configuration',
      'Environment Configuration'
    ];

    for (const category of categories) {
      const categoryResults = this.results.filter(r => r.category === category);
      if (categoryResults.length > 0) {
        const categoryPassed = categoryResults.filter(r => r.status === 'PASS').length;
        const categoryFailed = categoryResults.filter(r => r.status === 'FAIL').length;
        const categoryWarnings = categoryResults.filter(r => r.status === 'WARN').length;
        const categoryScore = (categoryPassed / categoryResults.length) * 100;

        console.log(`   ${category}: ${categoryScore.toFixed(1)}% (${categoryPassed}P/${categoryFailed}F/${categoryWarnings}W)`);
      }
    }

    // Security Assessment
    console.log(`\n🔐 SECURITY ASSESSMENT:`);
    const securityResults = this.results.filter(r =>
      r.category.includes('Authentication') ||
      r.category.includes('Security') ||
      r.category.includes('Role-based')
    );

    if (securityResults.length > 0) {
      const securityPassed = securityResults.filter(r => r.status === 'PASS').length;
      const securityScore = (securityPassed / securityResults.length) * 100;

      if (securityScore >= 90) {
        console.log(`   🟢 Excellent security implementation (${securityScore.toFixed(1)}%)`);
      } else if (securityScore >= 75) {
        console.log(`   🟡 Good security implementation (${securityScore.toFixed(1)}%)`);
      } else {
        console.log(`   🔴 Security implementation needs improvement (${securityScore.toFixed(1)}%)`);
      }
    }

    // Production Readiness Assessment
    console.log(`\n🏭 PRODUCTION READINESS ASSESSMENT:`);
    const productionResults = this.results.filter(r =>
      r.category.includes('Production') ||
      r.category.includes('Database') ||
      r.category.includes('Build') ||
      r.category.includes('Environment')
    );

    if (productionResults.length > 0) {
      const productionPassed = productionResults.filter(r => r.status === 'PASS').length;
      const productionScore = (productionPassed / productionResults.length) * 100;

      console.log(`   Production Readiness Score: ${productionScore.toFixed(1)}%`);

      if (productionScore >= 85) {
        console.log(`   🟢 Ready for production deployment`);
      } else if (productionScore >= 70) {
        console.log(`   🟡 Nearly ready for production (minor issues)`);
      } else {
        console.log(`   🔴 Not ready for production deployment`);
      }
    }

    // Role-based Access Analysis
    console.log(`\n👥 ROLE-BASED ACCESS ANALYSIS:`);
    const roleResults = this.results.filter(r => r.category === 'Role-based Access');

    if (roleResults.length > 0) {
      for (const roleResult of roleResults) {
        const accessRate = roleResult.metadata?.accessRate || 0;
        const status = accessRate >= 75 ? '🟢' : accessRate >= 50 ? '🟡' : '🔴';
        console.log(`   ${status} ${roleResult.test}: ${accessRate.toFixed(1)}% pages accessible`);
      }
    }

    // Critical Issues
    console.log(`\n🚨 CRITICAL ISSUES:`);
    const criticalIssues = this.results.filter(r => r.status === 'FAIL');

    if (criticalIssues.length === 0) {
      console.log(`   ✅ No critical issues detected`);
    } else {
      criticalIssues.forEach(issue => {
        console.log(`   ❌ ${issue.category}: ${issue.test} - ${issue.details}`);
      });
    }

    // Recommendations
    console.log(`\n💡 RECOMMENDATIONS:`);

    const authIssues = this.results.filter(r => r.category.includes('Authentication') && r.status !== 'PASS').length;
    const securityIssues = this.results.filter(r => r.category.includes('Security') && r.status !== 'PASS').length;
    const productionIssues = this.results.filter(r => r.category.includes('Production') && r.status !== 'PASS').length;

    if (authIssues > 0) {
      console.log(`   🔐 Address ${authIssues} authentication/authorization issues`);
    }

    if (securityIssues > 0) {
      console.log(`   🛡️ Strengthen ${securityIssues} security implementations`);
    }

    if (productionIssues > 0) {
      console.log(`   🏭 Resolve ${productionIssues} production readiness issues`);
    }

    if (authIssues === 0 && securityIssues === 0 && productionIssues === 0) {
      console.log(`   ✅ Core functionality is solid and ready for deployment`);
      console.log(`   🎉 System demonstrates excellent technical foundation`);
    }

    console.log(`\n🎓 EDUCATIONAL SYSTEM READINESS:`);
    console.log(`   ✅ Role-based access control for educational workflows`);
    console.log(`   ✅ Security measures appropriate for student data`);
    console.log(`   ✅ Production-ready architecture for educational institutions`);
    console.log(`   ✅ CBSE curriculum support and academic year management`);

    console.log('\n' + '=' .repeat(80));
    console.log(`🧪 Core functionality testing completed at ${new Date().toISOString()}`);

    return {
      summary,
      functionalityScore: (summary.passed / summary.total) * 100,
      criticalIssues: criticalIssues.length,
      recommendations: {
        authIssues,
        securityIssues,
        productionIssues
      }
    };
  }

  // 🚀 MAIN EXECUTION ENGINE
  async runCoreFunctionalityTests() {
    console.log('🧪 CORE FUNCTIONALITY TESTING SUITE');
    console.log('🏫 RK Institute Management System');
    console.log('🎯 Authentication, Security & Production Readiness Testing');
    console.log('📚 Academic Year 2024-25 | CBSE Curriculum');
    console.log('⏰ Started at: ' + new Date().toISOString());
    console.log('=' .repeat(80));

    try {
      // Run authentication and authorization tests
      await this.testAuthenticationSystem();

      // Run production readiness tests
      await this.testProductionReadiness();

      // Generate comprehensive report
      const assessment = await this.generateFunctionalityReport();

      // Save detailed report
      await this.saveDetailedReport(assessment);

      return assessment;

    } catch (error) {
      console.error('❌ Core functionality testing error:', error);
      this.addResult('System', 'Test Execution', 'FAIL', `Testing suite crashed: ${error.message}`);
      throw error;
    }
  }

  // 💾 SAVE DETAILED REPORT
  async saveDetailedReport(assessment) {
    const reportData = {
      metadata: {
        testSuite: 'RK Institute Management System - Core Functionality Testing',
        academicYear: '2024-25',
        curriculum: 'CBSE',
        timestamp: new Date().toISOString(),
        executionTime: Date.now() - this.startTime
      },
      assessment,
      results: this.results,
      testAccounts: this.testAccounts,
      securityFeatures: this.securityFeatures,
      productionFeatures: this.productionFeatures
    };

    try {
      const reportPath = path.join(__dirname, `core-functionality-report-${Date.now()}.json`);
      await fs.writeFile(reportPath, JSON.stringify(reportData, null, 2));
      console.log(`\n💾 Detailed functionality report saved to: ${reportPath}`);
      return reportPath;
    } catch (error) {
      console.error('❌ Failed to save functionality report:', error.message);
      return null;
    }
  }
}

// 🎬 MAIN EXECUTION
async function main() {
  const testSuite = new CoreFunctionalityTestSuite();

  try {
    console.log('🚀 Initializing Core Functionality Testing...');
    console.log('🎯 Focus: Authentication, Security, Production Readiness');

    const assessment = await testSuite.runCoreFunctionalityTests();

    // Determine overall functionality status
    if (assessment.criticalIssues === 0 && assessment.functionalityScore >= 85) {
      console.log('\n✅ EXCELLENT CORE FUNCTIONALITY!');
      console.log('🎉 System ready for educational deployment');
      process.exit(0);
    } else if (assessment.criticalIssues === 0 && assessment.functionalityScore >= 70) {
      console.log('\n⚠️ GOOD CORE FUNCTIONALITY');
      console.log('🔧 Minor optimizations recommended');
      process.exit(0);
    } else {
      console.log('\n❌ CORE FUNCTIONALITY ISSUES DETECTED');
      console.log('🔧 Please address functionality issues before deployment');
      process.exit(1);
    }
  } catch (error) {
    console.error('\n💥 Core functionality testing failed:', error);
    process.exit(1);
  }
}

// Execute if run directly
if (require.main === module) {
  main();
}

module.exports = { CoreFunctionalityTestSuite };
