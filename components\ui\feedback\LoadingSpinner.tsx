'use client';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  message?: string;
  className?: string;
}

const sizeStyles = {
  sm: 'w-4 h-4',
  md: 'w-8 h-8',
  lg: 'w-12 h-12'
};

export default function LoadingSpinner({ 
  size = 'md', 
  message = 'Loading...', 
  className = '' 
}: LoadingSpinnerProps) {
  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <div className={`${sizeStyles[size]} animate-spin rounded-full border-2 border-gray-300 border-t-blue-600`}></div>
      {message && (
        <p className="mt-4 text-gray-600 text-lg">{message}</p>
      )}
    </div>
  );
}
