name: Branch Synchronization Monitor

on:
  schedule:
    # Run daily at 9 AM UTC to check branch synchronization
    - cron: '0 9 * * *'
  workflow_dispatch:
    # Allow manual triggering
  push:
    branches: [main]
    # Trigger when main branch is updated

jobs:
  check-branch-sync:
    name: Monitor Branch Synchronization
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Check if develop is behind main
        id: sync-check
        run: |
          git fetch origin main develop
          
          # Check if develop is behind main
          BEHIND_COUNT=$(git rev-list --count origin/develop..origin/main)
          echo "behind_count=$BEHIND_COUNT" >> $GITHUB_OUTPUT
          
          if [ "$BEHIND_COUNT" -gt 0 ]; then
            echo "🚨 WORKFLOW VIOLATION: develop branch is $BEHIND_COUNT commits behind main"
            echo "violation=true" >> $GITHUB_OUTPUT
            
            # Get the commits that are missing
            echo "Missing commits:" >> sync_report.txt
            git log --oneline origin/develop..origin/main >> sync_report.txt
          else
            echo "✅ Branch synchronization is healthy"
            echo "violation=false" >> $GITHUB_OUTPUT
          fi

      - name: Create Issue for Branch Sync Violation
        if: steps.sync-check.outputs.violation == 'true'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const syncReport = fs.readFileSync('sync_report.txt', 'utf8');
            
            const issueBody = `## 🚨 **CRITICAL: Branch Synchronization Violation Detected**

            ### **Issue Description**
            The \`develop\` branch is **${{ steps.sync-check.outputs.behind_count }} commits behind** the \`main\` branch, violating our Professional Development & CI/CD Protocol v2.0.

            ### **Missing Commits**
            \`\`\`
            ${syncReport}
            \`\`\`

            ### **Required Action**
            This violation must be resolved immediately following the emergency protocol:

            1. **Immediate Sync Required**:
               \`\`\`bash
               git checkout develop
               git merge main
               git push origin develop
               \`\`\`

            2. **Verify Synchronization**:
               - Ensure all tests pass
               - Confirm no conflicts exist
               - Validate deployment works correctly

            3. **Prevent Future Violations**:
               - Review why direct push to main occurred
               - Ensure branch protection rules are enforced
               - Update team on proper workflow

            ### **Workflow Compliance**
            - ❌ **Branch Integrity**: VIOLATED
            - ❌ **GitFlow Protocol**: NOT FOLLOWED
            - ⚠️ **Production Risk**: HIGH

            ### **Auto-Resolution**
            This issue will be automatically closed when branch synchronization is restored.

            ---
            **Detected by**: Automated Branch Synchronization Monitor  
            **Detection Time**: ${new Date().toISOString()}  
            **Severity**: CRITICAL  
            **Protocol**: Professional Development & CI/CD Protocol v2.0`;

            await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: '🚨 CRITICAL: Branch Synchronization Violation - develop behind main',
              body: issueBody,
              labels: ['critical', 'workflow-violation', 'branch-sync', 'urgent']
            });

      - name: Send Slack Notification (if configured)
        if: steps.sync-check.outputs.violation == 'true'
        run: |
          echo "🚨 Branch synchronization violation detected!"
          echo "This would send a Slack notification if SLACK_WEBHOOK_URL is configured"
          # Uncomment and configure if Slack integration is needed:
          # curl -X POST -H 'Content-type: application/json' \
          #   --data '{"text":"🚨 CRITICAL: develop branch is behind main by ${{ steps.sync-check.outputs.behind_count }} commits"}' \
          #   ${{ secrets.SLACK_WEBHOOK_URL }}

  auto-close-resolved-issues:
    name: Auto-close Resolved Sync Issues
    runs-on: ubuntu-latest
    if: always()
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check if branches are synchronized
        id: current-sync
        run: |
          git fetch origin main develop
          BEHIND_COUNT=$(git rev-list --count origin/develop..origin/main)
          
          if [ "$BEHIND_COUNT" -eq 0 ]; then
            echo "synchronized=true" >> $GITHUB_OUTPUT
          else
            echo "synchronized=false" >> $GITHUB_OUTPUT
          fi

      - name: Close resolved sync issues
        if: steps.current-sync.outputs.synchronized == 'true'
        uses: actions/github-script@v7
        with:
          script: |
            // Find open branch sync violation issues
            const issues = await github.rest.issues.listForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: 'branch-sync,workflow-violation',
              state: 'open'
            });

            for (const issue of issues.data) {
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: issue.number,
                body: `## ✅ **Branch Synchronization Restored**

                The branch synchronization violation has been resolved. The \`develop\` and \`main\` branches are now properly synchronized.

                **Resolution confirmed by**: Automated Branch Synchronization Monitor  
                **Resolution time**: ${new Date().toISOString()}  
                **Status**: ✅ RESOLVED

                Closing this issue automatically.`
              });

              await github.rest.issues.update({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: issue.number,
                state: 'closed'
              });
            }
