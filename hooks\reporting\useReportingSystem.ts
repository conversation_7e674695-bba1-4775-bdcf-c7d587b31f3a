/**
 * Reporting System Hook
 * 
 * Custom hook for managing reporting system data and operations.
 * Follows the established pattern from our refactored architecture.
 * 
 * Features:
 * - Template management and filtering
 * - Report generation and tracking
 * - History management with FIFO cleanup
 * - Real-time status updates
 * - Error handling and retry logic
 */

'use client';

import { useState, useCallback, useEffect } from 'react';
import { 
  ReportTemplate, 
  GeneratedReport, 
  ReportStats,
  ReportGenerationRequest 
} from '@/components/features/reporting-system/types';

export interface UseReportingSystemReturn {
  // Data State
  templates: ReportTemplate[];
  reports: GeneratedReport[];
  stats: ReportStats;
  loading: boolean;
  error: string | null;
  
  // UI State
  selectedTemplate: ReportTemplate | null;
  generating: boolean;
  
  // Actions
  fetchTemplates: () => Promise<void>;
  fetchReports: () => Promise<void>;
  fetchStats: () => Promise<void>;
  selectTemplate: (template: ReportTemplate | null) => void;
  generateReport: (templateId: string, parameters: Record<string, any>, format: string) => Promise<boolean>;
  downloadReport: (reportId: string) => Promise<void>;
  deleteReport: (reportId: string) => Promise<boolean>;
  refreshReportStatus: (reportId: string) => Promise<void>;
}

export function useReportingSystem(
  userRole: string,
  userId: string
): UseReportingSystemReturn {
  
  // Data State
  const [templates, setTemplates] = useState<ReportTemplate[]>([]);
  const [reports, setReports] = useState<GeneratedReport[]>([]);
  const [stats, setStats] = useState<ReportStats>({
    totalReports: 0,
    reportsThisMonth: 0,
    popularTemplates: [],
    averageGenerationTime: 0,
    successRate: 0,
    storageUsed: 0
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // UI State
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null);
  const [generating, setGenerating] = useState(false);

  // Fetch report templates
  const fetchTemplates = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/reports/templates?role=${userRole}`, {
        headers: { 'Authorization': `Bearer ${token}` },
      });
      
      if (response.ok) {
        const data = await response.json();
        setTemplates(data.templates || []);
      } else {
        // Provide mock templates for development
        setTemplates(getMockTemplates(userRole));
      }
    } catch (err) {
      setError('Failed to fetch report templates');
      console.error('Error fetching templates:', err);
      // Provide mock templates as fallback
      setTemplates(getMockTemplates(userRole));
    } finally {
      setLoading(false);
    }
  }, [userRole]);

  // Fetch user's reports
  const fetchReports = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/reports/history?userId=${userId}`, {
        headers: { 'Authorization': `Bearer ${token}` },
      });
      
      if (response.ok) {
        const data = await response.json();
        setReports(data.reports || []);
      } else {
        // Provide mock reports for development
        setReports(getMockReports());
      }
    } catch (err) {
      setError('Failed to fetch report history');
      console.error('Error fetching reports:', err);
      // Provide mock reports as fallback
      setReports(getMockReports());
    } finally {
      setLoading(false);
    }
  }, [userId]);

  // Fetch reporting statistics
  const fetchStats = useCallback(async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/reports/stats?userId=${userId}`, {
        headers: { 'Authorization': `Bearer ${token}` },
      });
      
      if (response.ok) {
        const data = await response.json();
        setStats(data.stats);
      } else {
        // Provide mock stats for development
        setStats(getMockStats());
      }
    } catch (err) {
      console.error('Error fetching stats:', err);
      // Provide mock stats as fallback
      setStats(getMockStats());
    }
  }, [userId]);

  // Select template
  const selectTemplate = useCallback((template: ReportTemplate | null) => {
    setSelectedTemplate(template);
  }, []);

  // Generate report
  const generateReport = useCallback(async (
    templateId: string, 
    parameters: Record<string, any>, 
    format: string
  ): Promise<boolean> => {
    try {
      setGenerating(true);
      setError(null);
      
      const token = localStorage.getItem('token');
      const request: ReportGenerationRequest = {
        templateId,
        parameters,
        format: format as 'pdf' | 'excel' | 'csv',
        userId,
        userRole
      };
      
      const response = await fetch('/api/reports/generate', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });
      
      if (response.ok) {
        const data = await response.json();
        const newReport = data.report;
        
        // Add new report to the list
        setReports(prev => [newReport, ...prev]);
        
        // Start polling for status updates
        pollReportStatus(newReport.id);
        
        return true;
      } else {
        setError('Failed to generate report');
        return false;
      }
    } catch (err) {
      setError('Network error while generating report');
      console.error('Error generating report:', err);
      return false;
    } finally {
      setGenerating(false);
    }
  }, [userId, userRole]);

  // Poll report status
  const pollReportStatus = useCallback(async (reportId: string) => {
    const maxAttempts = 30; // 5 minutes with 10-second intervals
    let attempts = 0;
    
    const poll = async () => {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`/api/reports/${reportId}/status`, {
          headers: { 'Authorization': `Bearer ${token}` },
        });
        
        if (response.ok) {
          const data = await response.json();
          const updatedReport = data.report;
          
          // Update report in the list
          setReports(prev => prev.map(report => 
            report.id === reportId ? updatedReport : report
          ));
          
          // Continue polling if still generating
          if (updatedReport.status === 'generating' && attempts < maxAttempts) {
            attempts++;
            setTimeout(poll, 10000); // Poll every 10 seconds
          }
        }
      } catch (err) {
        console.error('Error polling report status:', err);
      }
    };
    
    poll();
  }, []);

  // Download report
  const downloadReport = useCallback(async (reportId: string) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/reports/${reportId}/download`, {
        headers: { 'Authorization': `Bearer ${token}` },
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `report-${reportId}.pdf`; // This would be dynamic based on format
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        // Update download count
        setReports(prev => prev.map(report => 
          report.id === reportId 
            ? { ...report, downloadCount: report.downloadCount + 1 }
            : report
        ));
      } else {
        setError('Failed to download report');
      }
    } catch (err) {
      setError('Network error while downloading report');
      console.error('Error downloading report:', err);
    }
  }, []);

  // Delete report
  const deleteReport = useCallback(async (reportId: string): Promise<boolean> => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/reports/${reportId}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` },
      });
      
      if (response.ok) {
        setReports(prev => prev.filter(report => report.id !== reportId));
        return true;
      } else {
        setError('Failed to delete report');
        return false;
      }
    } catch (err) {
      setError('Network error while deleting report');
      console.error('Error deleting report:', err);
      return false;
    }
  }, []);

  // Refresh report status
  const refreshReportStatus = useCallback(async (reportId: string) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/reports/${reportId}/status`, {
        headers: { 'Authorization': `Bearer ${token}` },
      });
      
      if (response.ok) {
        const data = await response.json();
        const updatedReport = data.report;
        
        setReports(prev => prev.map(report => 
          report.id === reportId ? updatedReport : report
        ));
      }
    } catch (err) {
      console.error('Error refreshing report status:', err);
    }
  }, []);

  // Initialize data on mount
  useEffect(() => {
    fetchTemplates();
    fetchReports();
    fetchStats();
  }, [fetchTemplates, fetchReports, fetchStats]);

  return {
    // Data State
    templates,
    reports,
    stats,
    loading,
    error,
    
    // UI State
    selectedTemplate,
    generating,
    
    // Actions
    fetchTemplates,
    fetchReports,
    fetchStats,
    selectTemplate,
    generateReport,
    downloadReport,
    deleteReport,
    refreshReportStatus,
  };
}

// Mock data functions for development
function getMockTemplates(userRole: string): ReportTemplate[] {
  const allTemplates = [
    {
      id: 'academic-progress',
      name: 'Academic Progress Report',
      description: 'Comprehensive academic performance analysis for students',
      category: 'academic' as const,
      userRoles: ['admin', 'teacher', 'parent'] as ('admin' | 'teacher' | 'parent' | 'student')[],
      parameters: [
        {
          id: 'studentId',
          name: 'studentId',
          label: 'Student',
          type: 'select' as const,
          required: true,
          options: [
            { value: 'student-1', label: 'Emma Johnson' },
            { value: 'student-2', label: 'Liam Johnson' }
          ]
        },
        {
          id: 'dateRange',
          name: 'dateRange',
          label: 'Report Period',
          type: 'select' as const,
          required: true,
          defaultValue: 'current-term',
          options: [
            { value: 'current-term', label: 'Current Term' },
            { value: 'last-term', label: 'Last Term' },
            { value: 'academic-year', label: 'Full Academic Year' }
          ]
        }
      ],
      outputFormats: ['pdf', 'excel'],
      estimatedTime: 15,
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 'financial-summary',
      name: 'Financial Summary Report',
      description: 'Detailed financial overview including fees, payments, and outstanding amounts',
      category: 'financial' as const,
      userRoles: ['admin', 'parent'] as ('admin' | 'teacher' | 'parent' | 'student')[],
      parameters: [
        {
          id: 'period',
          name: 'period',
          label: 'Period',
          type: 'select' as const,
          required: true,
          defaultValue: 'current-month',
          options: [
            { value: 'current-month', label: 'Current Month' },
            { value: 'last-month', label: 'Last Month' },
            { value: 'quarter', label: 'Current Quarter' },
            { value: 'year', label: 'Academic Year' }
          ]
        }
      ],
      outputFormats: ['pdf', 'excel', 'csv'],
      estimatedTime: 10,
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    }
  ];

  return allTemplates.filter(template => 
    template.userRoles.includes(userRole as any)
  );
}

function getMockReports(): GeneratedReport[] {
  return [
    {
      id: 'report-1',
      templateId: 'academic-progress',
      templateName: 'Academic Progress Report',
      userId: 'user-1',
      userRole: 'parent',
      parameters: { studentId: 'student-1', dateRange: 'current-term' },
      status: 'completed',
      format: 'pdf',
      fileUrl: '/reports/report-1.pdf',
      fileSize: 2048576, // 2MB
      generatedAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
      expiresAt: new Date(Date.now() + 6 * 86400000).toISOString(), // 6 days from now
      downloadCount: 2
    }
  ];
}

function getMockStats(): ReportStats {
  return {
    totalReports: 15,
    reportsThisMonth: 8,
    popularTemplates: [
      { templateId: 'academic-progress', templateName: 'Academic Progress Report', usageCount: 12 },
      { templateId: 'financial-summary', templateName: 'Financial Summary Report', usageCount: 8 }
    ],
    averageGenerationTime: 12,
    successRate: 95,
    storageUsed: 45.2
  };
}
