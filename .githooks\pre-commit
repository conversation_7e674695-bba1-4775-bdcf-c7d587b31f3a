#!/bin/sh
# RK Institute Management System - Pre-commit Hook
# Professional Development & CI/CD Protocol v2.0 Enforcement

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo "${BLUE}🔍 Running pre-commit checks...${NC}"

# Get current branch name
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)

# Check if trying to commit directly to protected branches
if [ "$CURRENT_BRANCH" = "main" ] || [ "$CURRENT_BRANCH" = "develop" ]; then
    echo "${RED}❌ WORKFLOW VIOLATION: Direct commits to '$CURRENT_BRANCH' branch are prohibited!${NC}"
    echo "${YELLOW}📋 Professional Development & CI/CD Protocol v2.0 requires:${NC}"
    echo "   1. Create a feature branch: ${GREEN}git checkout -b feature/your-feature-name${NC}"
    echo "   2. Make your changes on the feature branch"
    echo "   3. Create a Pull Request to merge into develop"
    echo ""
    echo "${YELLOW}💡 To fix this:${NC}"
    echo "   ${GREEN}git checkout -b feature/$(date +%Y%m%d)-fix${NC}"
    echo "   ${GREEN}git commit -m \"your commit message\"${NC}"
    echo ""
    exit 1
fi

# Validate branch naming convention
if [[ ! "$CURRENT_BRANCH" =~ ^(feature|hotfix|bugfix|chore)/.+ ]]; then
    echo "${YELLOW}⚠️  WARNING: Branch name '$CURRENT_BRANCH' doesn't follow naming convention${NC}"
    echo "${BLUE}📋 Recommended format: feature/description, hotfix/description, bugfix/description${NC}"
    echo "${YELLOW}Continue anyway? (y/N)${NC}"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        echo "${RED}❌ Commit cancelled. Please rename your branch.${NC}"
        exit 1
    fi
fi

# Check if develop is behind main (workflow compliance)
echo "${BLUE}🔄 Checking branch synchronization...${NC}"
git fetch origin main develop 2>/dev/null

if git rev-list --count origin/develop..origin/main >/dev/null 2>&1; then
    BEHIND_COUNT=$(git rev-list --count origin/develop..origin/main 2>/dev/null || echo "0")
    if [ "$BEHIND_COUNT" -gt 0 ]; then
        echo "${RED}❌ WORKFLOW VIOLATION: develop branch is $BEHIND_COUNT commits behind main${NC}"
        echo "${YELLOW}📋 Before continuing development, synchronize branches:${NC}"
        echo "   ${GREEN}git checkout develop${NC}"
        echo "   ${GREEN}git merge main${NC}"
        echo "   ${GREEN}git push origin develop${NC}"
        echo "   ${GREEN}git checkout $CURRENT_BRANCH${NC}"
        echo "   ${GREEN}git rebase develop${NC}"
        echo ""
        exit 1
    fi
fi

# Validate commit message format (Conventional Commits)
COMMIT_MSG_FILE="$1"
if [ -n "$COMMIT_MSG_FILE" ]; then
    COMMIT_MSG=$(cat "$COMMIT_MSG_FILE")
    if [[ ! "$COMMIT_MSG" =~ ^(feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert)(\(.+\))?: .+ ]]; then
        echo "${RED}❌ COMMIT MESSAGE VIOLATION: Must follow Conventional Commits format${NC}"
        echo "${YELLOW}📋 Required format: type(scope): description${NC}"
        echo "${BLUE}Examples:${NC}"
        echo "   ${GREEN}feat(auth): add user authentication${NC}"
        echo "   ${GREEN}fix(dashboard): resolve navigation bug${NC}"
        echo "   ${GREEN}docs(readme): update installation guide${NC}"
        echo ""
        exit 1
    fi
fi

# Run linting
echo "${BLUE}🔍 Running ESLint...${NC}"
if command -v npm >/dev/null 2>&1; then
    if ! npm run lint:check >/dev/null 2>&1; then
        echo "${RED}❌ ESLint errors found. Please fix before committing.${NC}"
        echo "${YELLOW}💡 Run: ${GREEN}npm run lint:fix${NC}"
        exit 1
    fi
    echo "${GREEN}✅ ESLint passed${NC}"
else
    echo "${YELLOW}⚠️  npm not found, skipping lint check${NC}"
fi

# Run TypeScript check
echo "${BLUE}🔍 Running TypeScript check...${NC}"
if command -v npx >/dev/null 2>&1; then
    if ! npx tsc --noEmit >/dev/null 2>&1; then
        echo "${RED}❌ TypeScript errors found. Please fix before committing.${NC}"
        echo "${YELLOW}💡 Run: ${GREEN}npx tsc --noEmit${NC}"
        exit 1
    fi
    echo "${GREEN}✅ TypeScript check passed${NC}"
else
    echo "${YELLOW}⚠️  TypeScript not found, skipping type check${NC}"
fi

# Check for sensitive information
echo "${BLUE}🔍 Scanning for sensitive information...${NC}"
STAGED_FILES=$(git diff --cached --name-only)

for file in $STAGED_FILES; do
    if [ -f "$file" ]; then
        # Check for common sensitive patterns
        if grep -qE "(password|secret|key|token|api_key|private_key)" "$file" 2>/dev/null; then
            if grep -qE "(password|secret|key|token|api_key|private_key)\s*[:=]\s*['\"][^'\"]{8,}" "$file" 2>/dev/null; then
                echo "${RED}❌ SECURITY VIOLATION: Potential sensitive information detected in $file${NC}"
                echo "${YELLOW}📋 Please remove sensitive data and use environment variables${NC}"
                exit 1
            fi
        fi
    fi
done
echo "${GREEN}✅ Security scan passed${NC}"

# Check file sizes
echo "${BLUE}🔍 Checking file sizes...${NC}"
for file in $STAGED_FILES; do
    if [ -f "$file" ]; then
        SIZE=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo 0)
        if [ "$SIZE" -gt 10485760 ]; then  # 10MB
            echo "${RED}❌ FILE SIZE VIOLATION: $file is larger than 10MB${NC}"
            echo "${YELLOW}📋 Large files should be stored using Git LFS${NC}"
            exit 1
        fi
    fi
done
echo "${GREEN}✅ File size check passed${NC}"

echo "${GREEN}🎉 All pre-commit checks passed! Proceeding with commit...${NC}"
exit 0
